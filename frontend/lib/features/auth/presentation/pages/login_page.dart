import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/constants/app_constants.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/back_button_handler.dart';
import '../../../../core/di/dependency_injection.dart';
import '../../../../shared/widgets/custom_text_field.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../data/models/saved_profile_model.dart';
import '../../domain/services/profile_storage_service.dart';
import '../bloc/auth_bloc.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;

  bool _showLoginForm = false;
  List<SavedProfileModel> _savedProfiles = [];
  bool _isLoadingProfiles = true;

  @override
  void initState() {
    super.initState();
    _loadSavedProfiles();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedProfiles() async {
    try {
      final profileStorage = DependencyInjection.getIt<ProfileStorageService>();
      final profiles = await profileStorage.getSavedProfiles();
      if (mounted) {
        setState(() {
          _savedProfiles = profiles;
          _isLoadingProfiles = false;
          // 如果沒有保存的 profile，直接顯示登錄表單
          _showLoginForm = profiles.isEmpty;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingProfiles = false;
          _showLoginForm = true;
        });
      }
    }
  }

  void _loginWithProfile(SavedProfileModel profile) async {
    // Try to auto-login using stored token first
    if (profile.accessToken != null && profile.accessToken!.isNotEmpty) {
      try {
        // Show loading state
        setState(() {
          _isLoadingProfiles = true;
        });

        // Try auto-login with stored token
        context.read<AuthBloc>().add(AuthTokenLoginRequested(
              accessToken: profile.accessToken!,
              userId: profile.id,
            ));

        // The BlocListener will handle the result
        return;
      } catch (e) {
        // Fall through to manual login
      }
    }

    // Fall back to manual login if no token or auto-login failed
    if (mounted) {
      // Auto-fill the form with selected profile's email
      _emailController.text = profile.email;
      // Switch to login form
      setState(() {
        _showLoginForm = true;
        _isLoadingProfiles = false;
      });
      // Focus on password field after a short delay
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          FocusScope.of(context).nextFocus();
        }
      });
    }
  }

  void _showLoginToExistingAccount() {
    setState(() {
      _showLoginForm = true;
    });
  }

  void _backToProfileList() {
    setState(() {
      _showLoginForm = false;
      _emailController.clear();
      _passwordController.clear();
    });
  }

  Future<void> _removeProfile(SavedProfileModel profile) async {
    final confirmed = await showModalBottomSheet<bool>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 12, bottom: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Header
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 8, 16, 16),
              child: Row(
                children: [
                  const Icon(Icons.person_remove, color: Colors.red),
                  const SizedBox(width: 8),
                  const Text(
                    'Remove Profile',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(false),
                    icon: const Icon(Icons.close),
                    iconSize: 20,
                  ),
                ],
              ),
            ),
            // Content
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Text(
                    'Are you sure you want to remove ${profile.displayName} from saved profiles?',
                    style: const TextStyle(fontSize: 16),
                  ),
                  const SizedBox(height: 24),
                  // Actions
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          style: TextButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                              side: BorderSide(color: Colors.grey[300]!),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Remove'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ],
        ),
      ),
    );

    if (confirmed == true) {
      final profileStorage = DependencyInjection.getIt<ProfileStorageService>();
      await profileStorage.removeProfile(profile.id);
      await _loadSavedProfiles();
    }
  }

  void _startOfflineSession() {
    // Navigate to offline chat page
    context.go('/offline-chat');
  }

  void _handleLogin() {
    if (_formKey.currentState!.validate()) {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      context.read<AuthBloc>().add(
            AuthLoginRequested(
              email: email,
              password: password,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler.wrapWithBackHandler(
      context: context,
      isMainPage: true,
      pageName: 'LoginPage',
      child: Scaffold(
        body: BlocListener<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthAuthenticated) {
              context.go('/home');
            } else if (state is AuthError) {
              // If token login failed and we're in loading state, fall back to manual login
              if (_isLoadingProfiles &&
                  state.message.contains('Auto-login failed')) {
                setState(() {
                  _isLoadingProfiles = false;
                  _showLoginForm = true;
                });
                // Don't show error message for auto-login failures
              } else {
                _showErrorMessage(context, state.message);
                // Reset loading state
                if (_isLoadingProfiles) {
                  setState(() {
                    _isLoadingProfiles = false;
                  });
                }
              }
            }
          },
          child: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height -
                      MediaQuery.of(context).padding.top -
                      MediaQuery.of(context).padding.bottom -
                      (AppConstants.defaultPadding * 2),
                ),
                child: IntrinsicHeight(
                  child: _isLoadingProfiles
                      ? _buildLoadingView()
                      : _showLoginForm
                          ? _buildLoginForm()
                          : _buildProfileList(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildProfileList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 40),

        // Header
        Column(
          children: [
            const Icon(
              Icons.auto_awesome,
              size: 64,
              color: AppColors.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Welcome Back',
              style: Theme.of(context).textTheme.displaySmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Choose your profile to continue',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppColors.textSecondary,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),

        const SizedBox(height: 48),

        // Profile List
        if (_savedProfiles.isNotEmpty) ...[
          ...(_savedProfiles.map((profile) => _buildProfileCard(profile))),
          const SizedBox(height: 16),
        ],

        // Login to Existing Account Button
        CustomButton(
          text: '+ Login to Existing Account',
          onPressed: _showLoginToExistingAccount,
          type: ButtonType.outline,
        ),

        const SizedBox(height: 24),

        // Register Link
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Don't have an account? ",
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            TextButton(
              onPressed: () => context.go('/register'),
              child: const Text('Sign Up'),
            ),
          ],
        ),

        const Spacer(),

        const SizedBox(height: 32),

        // Divider
        Row(
          children: [
            const Expanded(child: Divider()),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'OR',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppColors.textSecondary,
                    ),
              ),
            ),
            const Expanded(child: Divider()),
          ],
        ),

        const SizedBox(height: 32),

        // Offline Chat Button
        CustomButton(
          text: 'Start Offline Session',
          onPressed: _startOfflineSession,
          type: ButtonType.outline,
          icon: const Icon(Icons.wifi_tethering),
        ),

        const SizedBox(height: 16),

        // Offline Chat Description
        Text(
          'Chat with nearby friends without internet connection',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.textSecondary,
              ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildProfileCard(SavedProfileModel profile) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundImage: profile.avatarUrl != null
              ? NetworkImage(profile.avatarUrl!)
              : null,
          child: profile.avatarUrl == null
              ? Text(
                  profile.displayName.isNotEmpty
                      ? profile.displayName[0].toUpperCase()
                      : '?',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )
              : null,
        ),
        title: Text(
          profile.displayName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Text(profile.email),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'remove') {
              _removeProfile(profile);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 18),
                  SizedBox(width: 8),
                  Text('Remove'),
                ],
              ),
            ),
          ],
        ),
        onTap: () => _loginWithProfile(profile),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: 40),

          // Back Button (if there are saved profiles)
          if (_savedProfiles.isNotEmpty)
            Row(
              children: [
                IconButton(
                  onPressed: _backToProfileList,
                  icon: const Icon(Icons.arrow_back),
                ),
                const SizedBox(width: 8),
                Text(
                  'Back to Profiles',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),

          // Header
          Column(
            children: [
              const Icon(
                Icons.auto_awesome,
                size: 64,
                color: AppColors.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Welcome Back',
                style: Theme.of(context).textTheme.displaySmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Sign in to continue to ${AppConstants.appName}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),

          const SizedBox(height: 48),

          // Email Field
          CustomTextField(
            controller: _emailController,
            label: 'Email',
            hintText: 'Enter your email',
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.next,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email';
              }
              if (!RegExp(AppConstants.emailRegex).hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // Password Field
          CustomTextField(
            controller: _passwordController,
            label: 'Password',
            hintText: 'Enter your password',
            obscureText: _obscurePassword,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _handleLogin(),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              if (value.length < AppConstants.minPasswordLength) {
                return 'Password must be at least ${AppConstants.minPasswordLength} characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 32),

          // Login Button
          BlocBuilder<AuthBloc, AuthState>(
            builder: (context, state) {
              return CustomButton(
                text: 'Sign In',
                onPressed: state is AuthLoading ? null : _handleLogin,
                isLoading: state is AuthLoading,
              );
            },
          ),

          const SizedBox(height: 24),

          // Register Link
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                "Don't have an account? ",
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              TextButton(
                onPressed: () => context.go('/register'),
                child: const Text('Sign Up'),
              ),
            ],
          ),

          const Spacer(),

          const SizedBox(height: 32),

          // Divider
          Row(
            children: [
              const Expanded(child: Divider()),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'OR',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                ),
              ),
              const Expanded(child: Divider()),
            ],
          ),

          const SizedBox(height: 32),

          // Offline Chat Button
          CustomButton(
            text: 'Start Offline Session',
            onPressed: _startOfflineSession,
            type: ButtonType.outline,
            icon: const Icon(Icons.wifi_tethering),
          ),

          const SizedBox(height: 16),

          // Offline Chat Description
          Text(
            'Chat with nearby friends without internet connection',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  void _showErrorMessage(BuildContext context, String errorMessage) {
    // Convert backend error messages to user-friendly English messages
    String userFriendlyMessage = _getUserFriendlyErrorMessage(errorMessage);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                userFriendlyMessage,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Close',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  String _getUserFriendlyErrorMessage(String errorMessage) {
    // Convert common error messages to user-friendly English messages
    final lowerCaseError = errorMessage.toLowerCase();

    if (lowerCaseError.contains('invalid credentials') ||
        lowerCaseError.contains('unauthorized') ||
        lowerCaseError.contains('401')) {
      return 'Invalid email or password. Please check and try again.';
    } else if (lowerCaseError.contains('user not found') ||
        lowerCaseError.contains('user does not exist')) {
      return 'This email is not registered. Please sign up first.';
    } else if (lowerCaseError.contains('password') &&
        lowerCaseError.contains('incorrect')) {
      return 'Incorrect password. Please try again.';
    } else if (lowerCaseError.contains('email') &&
        lowerCaseError.contains('invalid')) {
      return 'Invalid email format. Please check and try again.';
    } else if (lowerCaseError.contains('network') ||
        lowerCaseError.contains('connection')) {
      return 'Network connection failed. Please check your connection and try again.';
    } else if (lowerCaseError.contains('timeout')) {
      return 'Request timeout. Please try again.';
    } else if (lowerCaseError.contains('server') ||
        lowerCaseError.contains('500')) {
      return 'Server temporarily unavailable. Please try again later.';
    } else if (lowerCaseError.contains('too many requests') ||
        lowerCaseError.contains('429')) {
      return 'Too many requests. Please try again later.';
    } else if (lowerCaseError.contains('account locked') ||
        lowerCaseError.contains('account disabled')) {
      return 'Account has been locked. Please contact support.';
    } else if (lowerCaseError.contains('validation failed') ||
        lowerCaseError.contains('bad request')) {
      return 'Invalid input. Please check your information and try again.';
    } else {
      // For unknown errors, show a generic message
      return 'Login failed. Please try again. If the problem persists, contact support.';
    }
  }
}
